<?php
// Include the shared header component
$pageTitle = "BM - Custom Billboard";
$headerTitle = "Custom Billboard Designer";
$headerSubtitle = "Create your perfect billboard design";
$headerIcon = "fas fa-paint-brush";
$additionalCSS = [
    "assets/css/custom-styles.css",
    "assets/css/custom-styles-body.css"
];
include '../shared/header.php';
?>

    <div class="cf7-text-editor-container">
        <!-- Canvas Area -->
        <div class="cf7-canvas" id="cf7-canvas" data-width="800" data-height="400">
            <div class="cf7-canvas-background" id="cf7-canvas-bg"></div>
            <div class="cf7-elements-container" id="cf7-elements"></div>
        </div>

        <!-- Toolbar -->
        <div class="cf7-toolbar">
            <!-- Background Image Selector and Action Buttons -->
            <div class="cf7-background-controls">
                <button class="cf7-btn-background-modal" id="cf7-bg-modal-trigger"
                        aria-label="Open background image selector">
                    Choose Background
                </button>
                <button class="cf7-btn-clear-bg" id="cf7-clear-bg"
                        aria-label="Clear background image">
                    Clear Background
                </button>

                <!-- Add Image Element -->
                <button type="button" class="cf7-btn-image" onclick="window.cf7Editors[0].triggerImageUpload()">Add Image</button>

                <!-- Clear Canvas -->
                <button type="button" class="cf7-btn-clear" onclick="window.cf7Editors[0].clearCanvas()">Clear All</button>
            </div>

            <!-- Font Controls -->
            <div class="cf7-font-controls">

                <!-- Font Family Control -->
                <div class="cf7-control-group">
                <!-- Add Text Element - First in font controls -->
                <button type="button" class="cf7-btn-text" onclick="window.cf7Editors[0].addTextElement()">Add Text</button>
                </div>

                <!-- Font Family Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Font:</label>
                    <select class="cf7-select-font" id="cf7-font-family">
                        <option value="Inter">Inter</option>
                        <option value="Roboto">Roboto</option>
                        <option value="Open Sans">Open Sans</option>
                        <option value="Lato">Lato</option>
                        <option value="Montserrat">Montserrat</option>
                        <option value="Poppins">Poppins</option>
                        <option value="Playfair Display">Playfair Display</option>
                        <option value="Merriweather">Merriweather</option>
                        <option value="Source Sans Pro">Source Sans Pro</option>
                        <option value="Nunito">Nunito</option>
                        <option value="Raleway">Raleway</option>
                        <option value="Oswald">Oswald</option>
                        <option value="Dancing Script">Dancing Script</option>
                        <option value="Pacifico">Pacifico</option>
                        <option value="Lobster">Lobster</option>
                    </select>
                </div>

                <!-- Font Size Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Size:</label>
                    <input type="number" class="cf7-input-size" id="cf7-font-size" value="16" min="8" max="200" step="1">
                </div>

                <!-- Font Style Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Style:</label>
                    <div class="cf7-style-buttons">
                        <button type="button" class="cf7-btn-style" id="cf7-font-bold" data-style="bold">B</button>
                        <button type="button" class="cf7-btn-style" id="cf7-font-italic" data-style="italic">I</button>
                    </div>
                </div>

                <!-- Text Alignment Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Align:</label>
                    <div class="cf7-style-buttons">
                        <button type="button" class="cf7-btn-style" id="cf7-align-left" data-align="left">
                            <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="cf7-btn-style" id="cf7-align-center" data-align="center">
                            <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="cf7-btn-style" id="cf7-align-right" data-align="right">
                            <i class="fas fa-align-right"></i>
                        </button>
                        <button type="button" class="cf7-btn-style" id="cf7-align-justify" data-align="justify">
                            <i class="fas fa-align-justify"></i>
                        </button>
                    </div>
                </div>

                <!-- Font Color Control -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Color:</label>
                    <input type="color" class="cf7-color-picker" id="cf7-font-color" value="#000000">
                </div>

                <!-- Text Shadow Controls -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Shadow:</label>
                    <div class="cf7-shadow-controls">
                        <button type="button" class="cf7-btn-shadow" id="cf7-text-shadow-toggle" data-toggle="shadow" title="Toggle Shadow">S</button>
                        <label for="cf7-shadow-color" class="cf7-shadow-label">Color:</label>
                        <input type="color" class="cf7-color-picker" id="cf7-shadow-color" value="#000000" title="Shadow Color">
                        <label for="cf7-shadow-blur" class="cf7-shadow-label">Blur:</label>
                        <input type="number" class="cf7-input-shadow-blur" id="cf7-shadow-blur" value="2" min="0" max="20" step="1" title="Shadow Blur">
                        <label for="cf7-shadow-offset-x" class="cf7-shadow-label">X:</label>
                        <input type="number" class="cf7-input-shadow-offset" id="cf7-shadow-offset-x" value="2" min="-20" max="20" step="1" title="Horizontal Offset">
                        <label for="cf7-shadow-offset-y" class="cf7-shadow-label">Y:</label>
                        <input type="number" class="cf7-input-shadow-offset" id="cf7-shadow-offset-y" value="2" min="-20" max="20" step="1" title="Vertical Offset">
                    </div>
                </div>

                <!-- Shadow Opacity Slider -->
                <div class="cf7-control-group">
                    <label class="cf7-control-label">Opacity:</label>
                    <div class="cf7-slider-container">
                        <input type="range" class="cf7-range-slider" id="cf7-shadow-opacity-slider" min="0" max="100" value="100" step="1">
                        <span class="cf7-slider-value" id="cf7-opacity-value">100%</span>
                    </div>
                </div>
            </div>

            <!-- Export and Payment Controls -->
            <div class="cf7-export-controls">
                <label class="cf7-control-label">Export & Order:</label>

                <!-- Export Options -->
                <div class="cf7-export-options">
                    <select id="cf7-export-quality" class="cf7-select-quality">
                        <option value="web">Web Quality</option>
                        <option value="standard" selected>Standard Quality</option>
                        <option value="high">High Quality</option>
                    </select>
                    <select id="cf7-export-format" class="cf7-select-format">
                        <option value="png" selected>PNG</option>
                        <option value="jpeg">JPEG</option>
                    </select>
                </div>

                <div class="cf7-export-buttons">
                    <button type="button" class="cf7-btn-download" id="cf7-export-image" onclick="window.cf7Editors[0].exportCanvasAsPNG()">
                        <i class="fas fa-download"></i>
                        Download Image
                    </button>
                    <button type="button" class="cf7-btn-export" id="cf7-proceed-payment" onclick="openUnifiedCheckout()">
                        <i class="fas fa-shopping-cart"></i>
                        Checkout
                    </button>
                </div>
            </div>
        </div>

        <!-- Background Image Modal -->
        <dialog class="cf7-background-modal" id="cf7-bg-modal" aria-labelledby="cf7-modal-title" aria-describedby="cf7-modal-desc">
            <div class="cf7-modal-content">
                <header class="cf7-modal-header">
                    <h2 id="cf7-modal-title" class="cf7-modal-title">Choose Background Image</h2>
                    <p id="cf7-modal-desc" class="cf7-modal-description">Select a category and image for your billboard background</p>
                    <button class="cf7-modal-close" id="cf7-modal-close" aria-label="Close background selector">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </header>

                <div class="cf7-modal-body">
                    <div class="cf7-modal-step cf7-step-category" id="cf7-step-category">
                        <h3 class="cf7-step-title">Step 1: Select Category</h3>
                        <div class="cf7-category-grid" id="cf7-category-grid" role="radiogroup" aria-labelledby="cf7-step-title">
                            <!-- Categories will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="cf7-modal-step cf7-step-template" id="cf7-step-template" style="display: none;">
                        <h3 class="cf7-step-title">Step 2: Select Image</h3>
                        <button class="cf7-back-btn" id="cf7-back-to-categories" aria-label="Back to categories">
                            ← Back to Categories
                        </button>
                        <div class="cf7-template-grid" id="cf7-template-grid" role="radiogroup" aria-labelledby="cf7-step-title">
                            <!-- Images will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <footer class="cf7-modal-footer">
                    <button class="cf7-btn-cancel" id="cf7-modal-cancel">Cancel</button>
                    <button class="cf7-btn-apply" id="cf7-modal-apply" disabled>Apply Background</button>
                </footer>
            </div>
        </dialog>

        <!-- Checkout Dialog - Using Background Modal Structure -->
        <dialog class="cf7-background-modal" id="checkoutDialog" aria-labelledby="checkout-modal-title" aria-describedby="checkout-modal-desc">
            <div class="cf7-modal-content">
                <header class="cf7-modal-header">
                    <h2 id="checkout-modal-title" class="cf7-modal-title">📋 Review Your Billboard Order</h2>
                    <p id="checkout-modal-desc" class="cf7-modal-description">Review your order details and agree to terms before proceeding to payment</p>
                    <button class="cf7-modal-close" id="checkout-modal-close" aria-label="Close checkout dialog" onclick="closeCheckoutDialog()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </header>

                <div class="cf7-modal-body">
                    <div class="cf7-modal-step" id="checkout-step-content">
                        <!-- Order Summary -->
                        <div class="order-summary">
                            <h3 class="cf7-step-title">📊 Order Summary</h3>
                            <!-- Purpose row hidden as requested -->
                            <div class="summary-row">
                                <span class="summary-label">Billboard Location:</span>
                                <span class="summary-value" id="dialogSummaryLocation">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Run Dates:</span>
                                <span class="summary-value" id="dialogSummaryDates">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Duration:</span>
                                <span class="summary-value" id="dialogSummaryDuration">Loading...</span>
                            </div>
                            <div class="summary-row">
                                <span class="summary-label">Total Cost:</span>
                                <span class="summary-value" id="dialogSummaryCost">$0.00</span>
                            </div>
                        </div>

                        <!-- Terms and Email Options -->
                        <div class="checkbox-section">
                            <h3 class="cf7-step-title">📋 Agreement & Requirements</h3>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_terms_agreement" id="dialog_terms_agreement" required>
                                <label for="dialog_terms_agreement" class="checkbox-text">
                                    I agree to the terms and conditions
                                    <span class="terms-link" onclick="openTermsModal()" style="margin-left: 10px; color: #007cba; text-decoration: underline; cursor: pointer; font-weight: 500;">Click here to read full terms</span>
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_content_compliance" id="dialog_content_compliance" required>
                                <label for="dialog_content_compliance" class="checkbox-text">
                                    I have NOT used photos of people smoking, drinking, hand symbols, showing too much skin.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_business_ad_compliance" id="dialog_business_ad_compliance" required>
                                <label for="dialog_business_ad_compliance" class="checkbox-text">
                                    My ad is not a business ad or promotional ad in any way.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_ad_preview_confirmation" id="dialog_ad_preview_confirmation" required>
                                <label for="dialog_ad_preview_confirmation" class="checkbox-text">
                                    The design above is exactly what my ad will look like. If it has to be changed, I will be charged $25.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_refund_policy_agreement" id="dialog_refund_policy_agreement" required>
                                <label for="dialog_refund_policy_agreement" class="checkbox-text">
                                    If your ad does not comply it will not play and you will not be refunded.
                                </label>
                            </div>

                            <div class="checkbox-item">
                                <input type="checkbox" name="dialog_email_copy" id="dialog_email_copy">
                                <label for="dialog_email_copy" class="checkbox-text">
                                    Email me a copy of this ad
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <footer class="cf7-modal-footer">
                    <button class="cf7-btn-cancel" id="checkout-modal-cancel" onclick="closeCheckoutDialog()">Cancel</button>
                    <button class="cf7-btn-apply" id="checkout-modal-proceed" onclick="proceedToPayment()">Proceed to Payment</button>
                </footer>
            </div>
        </dialog>

    </div>

    <!-- Include Unified Checkout Modal -->
    <?php include '../shared/checkout-modal.php'; ?>

    <!-- HTML-to-Image Library for Export Functionality -->
    <script src="https://cdn.jsdelivr.net/npm/html-to-image@1.11.13/dist/html-to-image.js"></script>

    <!-- Custom Billboard Functions - Modular Version -->
    <!-- Load all JavaScript modules in correct order -->

    <!-- 1. Core class definition and initialization -->
    <script src="assets/js/utility-func-and-initialization.js"></script>

    <!-- 2. Background template data -->
    <script src="assets/js/background-template-data.js"></script>

    <!-- 3. Core initialization methods -->
    <script src="assets/js/initialization-methods.js"></script>

    <!-- 4. Shortcode conversion utilities -->
    <script src="assets/js/shortcode-conversion-methods.js"></script>

    <!-- 5. Element management -->
    <script src="assets/js/element-management-methods.js"></script>

    <!-- 6. Drag and drop functionality -->
    <script src="assets/js/drag-and-drop-functionality.js"></script>

    <!-- 7. Font control setup -->
    <script src="assets/js/font-control-setup.js"></script>

    <!-- 8. Font management methods -->
    <script src="assets/js/font-management-methods.js"></script>

    <!-- 9. Background control setup -->
    <script src="assets/js/background-control-setup.js"></script>

    <!-- 10. Background management methods -->
    <script src="assets/js/background-management-methods.js"></script>

    <!-- 11. Export and canvas management -->
    <script src="assets/js/export-and-canvas-management.js"></script>

    <!-- 12. Checkout integration -->
    <script src="assets/js/checkout-integration.js"></script>

    <!-- Unified Checkout System -->
    <link rel="stylesheet" href="../shared/checkout-modal.css">
    <script src="../shared/order-data-manager.js"></script>
    <script src="../shared/high-quality-image-generator.js"></script>
    <script src="../shared/checkout-modal.js"></script>

</body>
</html>