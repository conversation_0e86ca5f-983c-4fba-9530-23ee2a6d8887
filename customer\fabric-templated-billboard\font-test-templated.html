<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Test - Templated Billboard Editor</title>
    
    <!-- Load the same fonts as the main application -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Core Google Fonts - Basic Typography -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700;900&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Display & Decorative Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Mouse+Memoirs&family=Domine:wght@400;500;600;700&family=Baloo+Tamma+2:wght@400;500;600;700;800&family=Courgette&family=Oswald:wght@300;400;500;600;700&family=Kaushan+Script&family=Alfa+Slab+One&family=Yellowtail&family=Paytone+One&display=swap" rel="stylesheet">
    
    <!-- Script & Handwriting Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&family=Dancing+Script:wght@400;500;600;700&family=Anton&family=Luckiest+Guy&family=Permanent+Marker&family=Coda:wght@400;800&family=Arvo:wght@400;700&family=Lobster&display=swap" rel="stylesheet">
    
    <!-- Fabric.js CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .test-section h2 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .font-dropdown {
            width: 100%;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
            display: flex;
            justify-content: center;
        }
        
        .font-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .font-sample {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            text-align: center;
        }
        
        .font-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .sample-text {
            font-size: 18px;
            color: #333;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Templated Billboard Editor - Font Test</h1>
        
        <div class="test-section">
            <h2>1. Font Dropdown Test</h2>
            <p>This dropdown should show all available fonts with proper font family styling:</p>
            <select id="fontDropdown" class="font-dropdown">
                <option value="">Select a font...</option>
            </select>
            <div id="dropdownStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>2. Fabric.js Canvas Test</h2>
            <p>This canvas will test font rendering in Fabric.js (same as the billboard editor):</p>
            <div class="canvas-container">
                <canvas id="testCanvas" width="600" height="200"></canvas>
            </div>
            <button onclick="testCanvasFont()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Test Random Font</button>
            <div id="canvasStatus" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Font Loading Status</h2>
            <div id="fontLoadingStatus" class="status">Checking font loading...</div>
            <div class="font-list" id="fontList"></div>
        </div>
    </div>
    
    <!-- Load FontManager -->
    <script src="../shared/font-manager.js"></script>
    
    <script>
        let canvas;
        let textObject;
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🔄 Starting font test...');
            
            // Initialize Fabric.js canvas
            canvas = new fabric.Canvas('testCanvas');
            
            // Add initial text
            textObject = new fabric.Text('Sample Text - Testing Fonts', {
                left: 50,
                top: 80,
                fontSize: 24,
                fill: '#333',
                fontFamily: 'Inter'
            });
            canvas.add(textObject);
            
            // Wait for FontManager to initialize
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Populate font dropdown
            populateFontDropdown();
            
            // Test font loading
            await testFontLoading();
            
            console.log('✅ Font test initialized');
        });
        
        function populateFontDropdown() {
            const dropdown = document.getElementById('fontDropdown');
            const status = document.getElementById('dropdownStatus');
            
            try {
                if (window.FontManager) {
                    const fonts = window.FontManager.getAllFonts();
                    dropdown.innerHTML = '<option value="">Select a font...</option>';
                    
                    fonts.forEach(font => {
                        const option = document.createElement('option');
                        option.value = font.name;
                        option.textContent = font.name;
                        option.style.fontFamily = font.family;
                        dropdown.appendChild(option);
                    });
                    
                    status.className = 'status success';
                    status.textContent = `✅ Font dropdown populated with ${fonts.length} fonts`;
                } else {
                    throw new Error('FontManager not available');
                }
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Error populating dropdown: ${error.message}`;
            }
            
            // Add change event
            dropdown.addEventListener('change', async (e) => {
                if (e.target.value && textObject) {
                    const fontName = e.target.value;
                    console.log('🎨 Testing font:', fontName);
                    
                    // Load font if FontManager available
                    if (window.FontManager) {
                        await window.FontManager.loadFont(fontName);
                    }
                    
                    // Apply to canvas text
                    textObject.set('fontFamily', fontName);
                    textObject.set('text', `Testing: ${fontName}`);
                    canvas.renderAll();
                }
            });
        }
        
        async function testFontLoading() {
            const status = document.getElementById('fontLoadingStatus');
            const fontList = document.getElementById('fontList');
            
            try {
                if (!window.FontManager) {
                    throw new Error('FontManager not available');
                }
                
                const testFonts = [
                    'Mouse Memoirs', 'Alfa Slab One', 'Kaushan Script',
                    'Dancing Script', 'Yellowtail', 'Permanent Marker',
                    'Courgette', 'Lobster', 'Indie Flower'
                ];
                
                status.className = 'status';
                status.textContent = '🔄 Testing font loading...';
                
                const results = [];
                for (const font of testFonts) {
                    const loaded = await window.FontManager.loadFont(font);
                    results.push({ font, loaded });
                    
                    // Create visual sample
                    const sample = document.createElement('div');
                    sample.className = 'font-sample';
                    sample.innerHTML = `
                        <div class="font-name">${font}</div>
                        <div class="sample-text" style="font-family: '${font}', sans-serif;">
                            The quick brown fox
                        </div>
                    `;
                    fontList.appendChild(sample);
                }
                
                const successful = results.filter(r => r.loaded).length;
                const failed = results.filter(r => !r.loaded).length;
                
                status.className = 'status success';
                status.textContent = `✅ Font loading test complete: ${successful} successful, ${failed} failed`;
                
            } catch (error) {
                status.className = 'status error';
                status.textContent = `❌ Font loading test failed: ${error.message}`;
            }
        }
        
        function testCanvasFont() {
            const status = document.getElementById('canvasStatus');
            
            if (!textObject) {
                status.className = 'status error';
                status.textContent = '❌ Canvas text object not available';
                return;
            }
            
            const testFonts = [
                'Mouse Memoirs', 'Alfa Slab One', 'Kaushan Script',
                'Dancing Script', 'Yellowtail', 'Permanent Marker'
            ];
            
            const randomFont = testFonts[Math.floor(Math.random() * testFonts.length)];
            
            textObject.set('fontFamily', randomFont);
            textObject.set('text', `Canvas Test: ${randomFont}`);
            canvas.renderAll();
            
            status.className = 'status success';
            status.textContent = `✅ Applied font: ${randomFont}`;
        }
    </script>
</body>
</html>
