/**
 * CanvasManager.js - Core canvas management for mobile-first Fabric.js editor
 * Handles canvas initialization, responsive scaling, and basic operations
 */

class CanvasManager {
    constructor(canvasId, options = {}) {
        this.canvasId = canvasId;
        this.canvas = null;
        this.container = null;
        this.originalWidth = options.width || 800;
        this.originalHeight = options.height || 400;
        this.scaleFactor = 1;
        this.isTouch = 'ontouchstart' in window;
        
        // Configuration
        this.config = {
            preserveObjectStacking: true,
            renderOnAddRemove: false,
            skipTargetFind: false,
            perPixelTargetFind: true,
            targetFindTolerance: this.isTouch ? 15 : 5,
            selection: true,
            ...options
        };
        
        this.init();
    }

    /**
     * Initialize the canvas and set up responsive behavior
     */
    init() {
        try {
            this.createCanvas();
            this.setupResponsive();
            this.bindEvents();
            this.setupMobileOptimizations();
            console.log('CanvasManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize CanvasManager:', error);
            throw error;
        }
    }

    /**
     * Create the Fabric.js canvas instance
     */
    createCanvas() {
        const canvasElement = document.getElementById(this.canvasId);
        if (!canvasElement) {
            throw new Error(`Canvas element with id '${this.canvasId}' not found`);
        }

        this.container = canvasElement.parentElement;
        
        // Create Fabric canvas with mobile-optimized settings
        this.canvas = new fabric.Canvas(this.canvasId, this.config);
        
        // Set initial dimensions
        this.canvas.setDimensions({
            width: this.originalWidth,
            height: this.originalHeight
        });

        // Remove black border - user requested removal
        this.canvas.wrapperEl.style.border = 'none';
    }

    /**
     * Set up responsive canvas scaling
     */
    setupResponsive() {
        this.updateCanvasSize();
        
        // Listen for window resize
        window.addEventListener('resize', this.debounce(() => {
            this.updateCanvasSize();
        }, 250));
        
        // Listen for orientation change on mobile
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.updateCanvasSize();
            }, 100);
        });
    }

    /**
     * Update canvas size based on container dimensions
     */
    updateCanvasSize() {
        if (!this.container || !this.canvas) return;

        const containerWidth = this.container.clientWidth;
        const containerHeight = this.container.clientHeight || (containerWidth * 0.5);
        
        // Calculate scale factor to fit container while maintaining aspect ratio
        const scaleX = containerWidth / this.originalWidth;
        const scaleY = containerHeight / this.originalHeight;
        this.scaleFactor = Math.min(scaleX, scaleY, 1); // Don't scale up beyond original size
        
        const scaledWidth = this.originalWidth * this.scaleFactor;
        const scaledHeight = this.originalHeight * this.scaleFactor;
        
        // Update canvas dimensions
        this.canvas.setDimensions({
            width: scaledWidth,
            height: scaledHeight
        });
        
        // Update zoom to maintain object proportions
        this.canvas.setZoom(this.scaleFactor);

        // Update background image scaling if it exists
        this.updateBackgroundImageScaling();

        // Center canvas in container
        this.centerCanvas();

        this.canvas.renderAll();
    }

    /**
     * Center canvas within its container
     */
    centerCanvas() {
        const canvasElement = this.canvas.wrapperEl;
        const containerWidth = this.container.clientWidth;
        const canvasWidth = canvasElement.offsetWidth;
        
        if (containerWidth > canvasWidth) {
            const leftMargin = (containerWidth - canvasWidth) / 2;
            canvasElement.style.marginLeft = `${leftMargin}px`;
            canvasElement.style.marginRight = `${leftMargin}px`;
        } else {
            canvasElement.style.marginLeft = '0';
            canvasElement.style.marginRight = '0';
        }
    }

    /**
     * Set up mobile-specific optimizations
     */
    setupMobileOptimizations() {
        if (!this.isTouch) return;

        // Prevent default touch behaviors that interfere with canvas
        this.canvas.wrapperEl.addEventListener('touchstart', (e) => {
            e.preventDefault();
        }, { passive: false });

        this.canvas.wrapperEl.addEventListener('touchmove', (e) => {
            e.preventDefault();
        }, { passive: false });

        // Optimize for touch interactions
        this.canvas.on('touch:gesture', (e) => {
            e.e.preventDefault();
            e.e.stopPropagation();
        });

        // Add visual feedback for touch selections
        this.canvas.on('selection:created', () => {
            this.addTouchFeedback();
        });

        this.canvas.on('selection:updated', () => {
            this.addTouchFeedback();
        });
    }

    /**
     * Add visual feedback for touch interactions
     */
    addTouchFeedback() {
        const activeObject = this.canvas.getActiveObject();
        if (activeObject && this.isTouch) {
            // Add subtle animation or visual cue for mobile users
            activeObject.animate('opacity', 0.8, {
                duration: 100,
                onChange: this.canvas.renderAll.bind(this.canvas),
                onComplete: () => {
                    activeObject.animate('opacity', 1, {
                        duration: 100,
                        onChange: this.canvas.renderAll.bind(this.canvas)
                    });
                }
            });
        }
    }

    /**
     * Bind canvas events
     */
    bindEvents() {
        // Object selection events
        this.canvas.on('selection:created', (e) => {
            this.emit('object:selected', e.selected[0]);
        });

        this.canvas.on('selection:updated', (e) => {
            this.emit('object:selected', e.selected[0]);
        });

        this.canvas.on('selection:cleared', () => {
            this.emit('object:deselected');
        });

        // Object modification events
        this.canvas.on('object:modified', (e) => {
            this.emit('object:modified', e.target);
        });

        // Canvas events
        this.canvas.on('canvas:cleared', () => {
            this.emit('canvas:cleared');
        });
    }

    /**
     * Clear the canvas
     */
    clear() {
        this.canvas.clear();
        this.canvas.renderAll();
    }

    /**
     * Get canvas instance
     */
    getCanvas() {
        return this.canvas;
    }

    /**
     * Get current scale factor
     */
    getScaleFactor() {
        return this.scaleFactor;
    }

    /**
     * Convert screen coordinates to canvas coordinates
     */
    screenToCanvas(screenX, screenY) {
        const canvasElement = this.canvas.upperCanvasEl;
        const rect = canvasElement.getBoundingClientRect();
        
        return {
            x: (screenX - rect.left) / this.scaleFactor,
            y: (screenY - rect.top) / this.scaleFactor
        };
    }

    /**
     * Update background image scaling when canvas is resized
     */
    updateBackgroundImageScaling() {
        const backgroundImage = this.canvas.backgroundImage;
        if (backgroundImage && backgroundImage.isBackground) {
            console.log('🔄 Updating background image scaling for canvas resize');

            // Recalculate scale for cover behavior using original dimensions
            const scaleX = this.originalWidth / backgroundImage.width;
            const scaleY = this.originalHeight / backgroundImage.height;
            const scale = Math.max(scaleX, scaleY);

            // Update background image properties
            backgroundImage.set({
                left: this.originalWidth / 2,
                top: this.originalHeight / 2,
                scaleX: scale,
                scaleY: scale
            });

            console.log('✅ Background image scaling updated');
        }
    }

    /**
     * Set background image with proper scaling (cover mode)
     */
    setBackgroundImage(imageUrl, callback) {
        fabric.Image.fromURL(imageUrl, (img) => {
            if (img) {
                console.log('🖼️ Loading background image:', imageUrl);
                console.log('📐 Canvas dimensions:', `${this.originalWidth}x${this.originalHeight}`);
                console.log('📐 Image dimensions:', `${img.width}x${img.height}`);

                // Calculate scale to cover entire canvas (like CSS background-size: cover)
                const scaleX = this.originalWidth / img.width;
                const scaleY = this.originalHeight / img.height;
                const scale = Math.max(scaleX, scaleY); // Use max for cover behavior

                console.log('📏 Scale factors:', { scaleX, scaleY, finalScale: scale });

                // Center the image and apply uniform scaling
                img.set({
                    left: this.originalWidth / 2,
                    top: this.originalHeight / 2,
                    originX: 'center',
                    originY: 'center',
                    scaleX: scale,
                    scaleY: scale, // Use same scale for both dimensions
                    selectable: false,
                    evented: false,
                    isBackground: true
                });

                this.canvas.setBackgroundImage(img, () => {
                    this.canvas.renderAll();
                    console.log('✅ Background image applied with cover scaling');
                    if (callback) callback(img);
                });
            } else {
                console.error('❌ Failed to load background image:', imageUrl);
                if (callback) callback(null);
            }
        }, {
            crossOrigin: 'anonymous'
        });
    }

    /**
     * Clear background image
     */
    clearBackgroundImage() {
        this.canvas.setBackgroundImage(null, () => {
            this.canvas.renderAll();
            console.log('🗑️ Background image cleared');
        });
    }

    /**
     * Export canvas as image
     */
    exportAsImage(format = 'png', quality = 1) {
        // Temporarily scale up for high-quality export
        const originalZoom = this.canvas.getZoom();
        const exportScale = 2; // 2x for HD quality

        this.canvas.setZoom(exportScale);
        this.canvas.setDimensions({
            width: this.originalWidth * exportScale,
            height: this.originalHeight * exportScale
        });

        const dataURL = this.canvas.toDataURL({
            format: format,
            quality: quality,
            multiplier: 1
        });

        // Restore original dimensions
        this.canvas.setZoom(originalZoom);
        this.updateCanvasSize();

        return dataURL;
    }

    /**
     * Simple event emitter
     */
    emit(eventName, data) {
        const event = new CustomEvent(`canvas:${eventName}`, { detail: data });
        document.dispatchEvent(event);
    }

    /**
     * Debounce utility function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Destroy canvas and clean up
     */
    destroy() {
        if (this.canvas) {
            this.canvas.dispose();
            this.canvas = null;
        }
        
        window.removeEventListener('resize', this.updateCanvasSize);
        window.removeEventListener('orientationchange', this.updateCanvasSize);
    }
}

// Export for use in other modules
window.CanvasManager = CanvasManager;
